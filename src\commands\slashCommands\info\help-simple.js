const {
    <PERSON><PERSON><PERSON>ommandBuilder,
    ActionRowBuilder,
    StringSelectMenuBuilder,
    EmbedBuilder
} = require('discord.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../../config/config.js');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('help-simple')
        .setDescription('Simple help command for debugging')
        .addStringOption(option =>
            option.setName('command')
                .setDescription('Get help for a specific command')
                .setRequired(false)
        ),
    name: 'help-simple',
    category: 'info',
    aliases: ['hs'],
    cooldown: 3,
    usage: 'help-simple [command_name]',
    description: 'Simple help command without canvas',
    OwnerOnly: false,
    ServerOwnerOnly: false,
    DevloperTeamOnly: false,
    CommandSupport: 'both',

    async execute(interaction) {
        try {
            console.log('🔍 Help-simple command started');
            
            const commandName = interaction.options.getString('command');
            const { commands, categories } = this.getCommandsAndCategories();
            
            console.log(`📊 Found ${commands.size} commands in ${categories.size} categories`);
            
            if (commandName) {
                return this.handleCommandHelp(interaction, commandName, commands);
            }
            
            // Create simple embed help menu
            const embed = new EmbedBuilder()
                .setColor(config.EmbedConfig.embedcolor)
                .setTitle('🤖 RankBreaker Help Menu')
                .setDescription('Select a category below to view commands')
                .addFields(
                    { name: '📊 Statistics', value: `${commands.size} commands in ${categories.size} categories`, inline: true },
                    { name: '🔧 Prefix', value: config.prefix, inline: true }
                )
                .setTimestamp()
                .setFooter({ 
                    text: `Requested by ${interaction.user.username}`, 
                    iconURL: interaction.user.displayAvatarURL({ dynamic: true }) 
                });

            // Create category select menu
            const selectMenu = new StringSelectMenuBuilder()
                .setCustomId('help_category_select')
                .setPlaceholder('🔍 Choose a category to explore...')
                .addOptions([
                    { label: 'Home', description: 'Return to main help menu', value: 'home', emoji: '🏠' },
                    ...[...categories.keys()].map(category => ({
                        label: this.formatCategoryName(category),
                        description: `View ${category} commands (${categories.get(category).length})`,
                        value: category,
                        emoji: this.getCategoryEmoji(category)
                    }))
                ]);

            const row = new ActionRowBuilder().addComponents(selectMenu);

            const reply = await interaction.reply({ 
                embeds: [embed], 
                components: [row], 
                fetchReply: true 
            });

            console.log('✅ Help menu sent successfully');

            // Set up collector
            const collector = reply.createMessageComponentCollector({
                filter: (i) => i.customId === 'help_category_select' && i.user.id === interaction.user.id,
                time: 120000,
            });

            collector.on('collect', async (menuInteraction) => {
                const selected = menuInteraction.values[0];
                console.log(`🎯 User selected: ${selected}`);

                if (selected === 'home') {
                    await menuInteraction.update({ embeds: [embed], components: [row] });
                } else {
                    const categoryCommands = categories.get(selected) || [];
                    const categoryEmbed = new EmbedBuilder()
                        .setColor(config.EmbedConfig.embedcolor)
                        .setTitle(`${this.getCategoryEmoji(selected)} ${this.formatCategoryName(selected)} Commands`)
                        .setDescription(
                            categoryCommands.length > 0 
                                ? categoryCommands.map(cmd => `**${cmd.name}** - ${cmd.description}`).join('\n')
                                : 'No commands in this category.'
                        )
                        .setFooter({ 
                            text: `${categoryCommands.length} command${categoryCommands.length !== 1 ? 's' : ''} | Use /help-simple [command] for details`, 
                            iconURL: interaction.user.displayAvatarURL({ dynamic: true }) 
                        })
                        .setTimestamp();

                    await menuInteraction.update({ embeds: [categoryEmbed], components: [row] });
                }
            });

            collector.on('end', () => {
                console.log('⏰ Help menu collector ended');
                const disabledRow = new ActionRowBuilder().addComponents(selectMenu.setDisabled(true));
                reply.edit({ components: [disabledRow] }).catch(() => {});
            });

        } catch (error) {
            console.error('❌ Error in help-simple command:', error);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Error')
                .setDescription(`An error occurred: ${error.message}`)
                .setTimestamp();
            
            if (interaction.replied) {
                interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
            } else {
                interaction.reply({ embeds: [errorEmbed], ephemeral: true });
            }
        }
    },

    async handleCommandHelp(interaction, commandName, commands) {
        const command = commands.get(commandName.toLowerCase()) || 
            [...commands.values()].find(cmd => cmd.aliases?.includes(commandName.toLowerCase()));

        if (!command) {
            return interaction.reply({ 
                content: `❌ Command \`${commandName}\` not found. Use \`/help-simple\` to see all commands.`,
                ephemeral: true 
            });
        }

        const embed = new EmbedBuilder()
            .setColor(config.EmbedConfig.embedcolor)
            .setTitle(`📋 ${command.name} Command`)
            .addFields(
                { name: '📝 Description', value: command.description || 'No description provided', inline: false },
                { name: '🏷️ Category', value: this.formatCategoryName(command.category), inline: true },
                { name: '⏱️ Cooldown', value: `${command.cooldown || 0} seconds`, inline: true },
                { name: '🔧 Usage', value: `\`${config.prefix}${command.usage}\``, inline: false }
            )
            .setTimestamp()
            .setFooter({ 
                text: `Requested by ${interaction.user.username}`, 
                iconURL: interaction.user.displayAvatarURL({ dynamic: true }) 
            });

        if (command.aliases && command.aliases.length > 0) {
            embed.addFields({ name: '🏷️ Aliases', value: command.aliases.join(', '), inline: true });
        }

        return interaction.reply({ embeds: [embed] });
    },

    getCommandsAndCategories() {
        const commands = new Map();
        const categories = new Map();
        
        const commandsPath = path.join(__dirname, '..');
        
        try {
            fs.readdirSync(commandsPath).forEach(category => {
                const categoryPath = path.join(commandsPath, category);
                if (fs.lstatSync(categoryPath).isDirectory()) {
                    const commandFiles = fs.readdirSync(categoryPath).filter(file => file.endsWith('.js'));
                    const categoryCommands = [];
                    
                    commandFiles.forEach(file => {
                        try {
                            // Skip the help command itself to avoid circular dependency
                            if (file === 'help.js' || file === 'help-simple.js') return;
                            
                            const command = require(path.join(categoryPath, file));
                            const commandData = {
                                name: command.name || command.data?.name || file.replace('.js', ''),
                                description: command.description || 'No description provided',
                                category: category,
                                usage: command.usage || command.name || file.replace('.js', ''),
                                cooldown: command.cooldown || 0,
                                aliases: command.aliases || []
                            };
                            
                            commands.set(commandData.name.toLowerCase(), commandData);
                            categoryCommands.push(commandData);
                        } catch (error) {
                            console.error(`Error loading command ${file}:`, error.message);
                        }
                    });
                    
                    if (categoryCommands.length > 0) {
                        categories.set(category, categoryCommands);
                    }
                }
            });
        } catch (error) {
            console.error('Error reading commands directory:', error);
        }
        
        return { commands, categories };
    },

    formatCategoryName(category) {
        return category ? category.charAt(0).toUpperCase() + category.slice(1).toLowerCase() : 'Unknown';
    },

    getCategoryEmoji(category) {
        const emojis = {
            'admin': '⚙️',
            'fun': '🎮',
            'info': 'ℹ️',
            'moderation': '🛡️',
            'utility': '🔧',
            'music': '🎵',
            'economy': '💰',
            'games': '🎯',
            'social': '👥'
        };
        return emojis[category.toLowerCase()] || '📚';
    }
};
