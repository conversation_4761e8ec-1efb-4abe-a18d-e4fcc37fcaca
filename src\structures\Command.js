/**
 * Base Command class for all bot commands
 */
class Command {
    constructor(options = {}) {
        this.name = options.name || options.data?.name || 'unknown';
        this.description = options.description || 'No description provided';
        this.category = options.category || 'general';
        this.usage = options.usage || this.name;
        this.aliases = options.aliases || [];
        this.cooldown = options.cooldown || 0;
        this.examples = options.examples || [];
        this.data = options.data || null;
        
        // Permissions and restrictions
        this.memberpermissions = options.memberpermissions || [];
        this.botpermissions = options.botpermissions || [];
        this.requiredroles = options.requiredroles || [];
        this.requiredchannels = options.requiredchannels || [];
        this.alloweduserids = options.alloweduserids || [];
        this.minargs = options.minargs || 0;
        this.maxargs = options.maxargs || 0;
        this.nsfw = options.nsfw || false;
        this.OwnerOnly = options.OwnerOnly || false;
        this.ServerOwnerOnly = options.ServerOwnerOnly || false;
        this.DevloperTeamOnly = options.DevloperTeamOnly || false;
        this.CommandSupport = options.CommandSupport || 'both';
        
        // Additional properties for help system
        this.ownerOnly = options.OwnerOnly || false;
        this.permissions = options.memberpermissions || [];
        this.voiceRequired = options.voiceRequired || false;
        this.sameVoiceRequired = options.sameVoiceRequired || false;
        this.playerRequired = options.playerRequired || false;
        this.playingRequired = options.playingRequired || false;
        this.customRequirements = options.customRequirements || [];
    }

    async execute(interaction) {
        throw new Error(`Execute method not implemented for command: ${this.name}`);
    }
}

module.exports = Command;
