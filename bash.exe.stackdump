Stack trace:
Frame         Function      Args
0007FFFFAAA0  00021006116E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF99A0) msys-2.0.dll+0x2116E
0007FFFFAAA0  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFFAAA0  0002100469F2 (00021028DF99, 0007FFFFA958, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFFAAA0  00021006A3FE (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A3FE
0007FFFFAAA0  00021006A525 (0007FFFFAAB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A525
0001004F94B7  00021006B985 (0007FFFFAAB0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B985
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFFC17B0000 ntdll.dll
7FFFC0370000 KERNEL32.DLL
7FFFBEC90000 KERNELBASE.dll
7FFFC0140000 USER32.dll
7FFFBEA40000 win32u.dll
7FFFC0BF0000 GDI32.dll
7FFFBF070000 gdi32full.dll
7FFFBF260000 msvcp_win.dll
7FFFBE8A0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFFC0550000 advapi32.dll
7FFFC07B0000 msvcrt.dll
7FFFC0A60000 sechost.dll
7FFFBEA70000 bcrypt.dll
7FFFBF480000 RPCRT4.dll
7FFFBE0B0000 CRYPTBASE.DLL
7FFFBE9C0000 bcryptPrimitives.dll
7FFFBF440000 IMM32.DLL
