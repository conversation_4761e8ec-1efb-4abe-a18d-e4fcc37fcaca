const {
    <PERSON><PERSON><PERSON><PERSON>mandB<PERSON>er,
    ActionRowBuilder,
    StringSelectMenuBuilder,
    ButtonBuilder,
    ButtonStyle,
    ComponentType,
    AttachmentBuilder,
    EmbedBuilder
} = require('discord.js');
const Command = require('../../../structures/Command.js');
const { themeManager } = require('../../../managers/ThemeManager.js');
const Timer = require('../../../utils/Timer.js');
const fs = require('fs');
const path = require('path');
const config = require('../../../../config/config.js');

const ITEMS_PER_PAGE = 24;
const TIMEOUT = 180000;

class HelpCommand extends Command {
    constructor() {
        super({
            data: new SlashCommandBuilder()
                .setName('help')
                .setDescription('Display help information about RankBreaker commands')
                .addStringOption(option =>
                    option.setName('command')
                        .setDescription('Get help for a specific command')
                        .setRequired(false)
                )
                .addBooleanOption(option =>
                    option.setName('msgspeed')
                        .setDescription('Show performance timing information')
                        .setRequired(false)
                ),
            name: 'help',
            category: 'info',
            aliases: ['h', 'commands'],
            cooldown: 3,
            usage: 'help [command_name] [--msgspeed]',
            description: 'Display help information about bot commands',
            examples: ['help', 'help ping', 'help queue'],
            OwnerOnly: false,
            ServerOwnerOnly: false,
            DevloperTeamOnly: false,
            CommandSupport: 'both'
        });
        
        this.collectors = new Map();
        this.cache = {
            categoryOptions: new Map(),
            filteredCommands: new Map(),
            components: new Map()
        };
    }

    async execute(interaction) {
        console.log('🚀 Help command execute started');
        
        // Immediate acknowledgment to prevent timeout
        await interaction.deferReply();
        console.log('✅ Interaction deferred');
        
        const timer = Timer.create();
        const showSpeed = interaction.options.getBoolean('msgspeed') || false;
        const commandName = interaction.options.getString('command');
        
        try {
            timer.mark('start');
            console.log('⏱️ Timer started');
            
            const { commands, categories } = this.getCommandsAndCategories(interaction.client);
            timer.mark('get-handlers');
            console.log(`📊 Loaded ${commands.size} commands in ${categories.size} categories`);
            
            const HelpCanvasClass = await themeManager.getHelpCanvasClass(interaction.guildId);
            timer.mark('theme-load');
            console.log('🎨 Theme loaded');
            
            const canvasManager = new HelpCanvasClass();
            timer.mark('canvas-init');
            console.log('🖼️ Canvas manager initialized');
            
            if (commandName) {
                console.log(`🔍 Handling specific command: ${commandName}`);
                return this.handleCommandHelp(interaction, commandName.toLowerCase(), commands, interaction.client.config?.prefix || config.prefix, interaction.client, canvasManager, timer, showSpeed);
            }
            
            console.log('🏠 Generating home page...');
            const [homePageBuffer, mainRow] = await Promise.all([
                canvasManager.generateHomePage(interaction.client, interaction.client.config?.prefix || config.prefix, categories),
                Promise.resolve(this.getMainSelectMenu(categories))
            ]);
            timer.mark('home-generation');
            console.log('✅ Home page generated');
            
            const homeAttachment = new AttachmentBuilder(homePageBuffer, { name: 'help-home.png' });
            timer.mark('attachment-create');
            console.log('📎 Attachment created');
            
            const helpMessage = await interaction.editReply({
                content: showSpeed ? timer.getReport() : undefined,
                files: [homeAttachment],
                components: [mainRow]
            });
            timer.mark('message-send');
            console.log('📤 Message sent successfully');
            
            this.setupCollectors(helpMessage, interaction.user.id, interaction.client, interaction.client.config?.prefix || config.prefix, categories, commands, canvasManager, showSpeed);
            timer.mark('collectors-setup');
            
            if (showSpeed) {
                setTimeout(() => {
                    helpMessage.edit({
                        content: timer.getReport(),
                        files: [homeAttachment],
                        components: [mainRow]
                    }).catch(() => {});
                }, 100);
            }
        } catch (error) {
            console.error('❌ HelpCommand Error:', error);
            console.error('Stack trace:', error.stack);
            
            const errorEmbed = new EmbedBuilder()
                .setColor('#ff0000')
                .setTitle('❌ Help Command Error')
                .setDescription(`An error occurred while displaying the help menu.\n\`\`\`${error.message}\`\`\``)
                .addFields(
                    { name: '🔧 Quick Fix', value: 'Try using `/help-simple` instead', inline: false },
                    { name: '📊 Debug Info', value: `Error at: ${timer.marks.size > 0 ? [...timer.marks.keys()].pop() : 'start'}`, inline: false }
                )
                .setTimestamp();
            
            try {
                if (interaction.deferred) {
                    await interaction.editReply({ embeds: [errorEmbed] });
                } else if (interaction.replied) {
                    await interaction.followUp({ embeds: [errorEmbed], ephemeral: true });
                } else {
                    await interaction.reply({ embeds: [errorEmbed], ephemeral: true });
                }
            } catch (replyError) {
                console.error('❌ Failed to send error message:', replyError);
            }
        }
    }

    getCommandsAndCategories(client) {
        const commands = new Map();
        const categories = new Map();

        const commandsPath = path.join(__dirname, '..');

        // Load all commands and categorize them
        fs.readdirSync(commandsPath).forEach(category => {
            const categoryPath = path.join(commandsPath, category);
            if (fs.lstatSync(categoryPath).isDirectory()) {
                const categoryCommands = [];

                // Load direct .js files in the category
                const commandFiles = fs.readdirSync(categoryPath).filter(file => file.endsWith('.js'));
                commandFiles.forEach(file => {
                    try {
                        const command = require(path.join(categoryPath, file));
                        const commandData = {
                            name: command.name || command.data?.name || file.replace('.js', ''),
                            description: command.description || 'No description provided',
                            category: category,
                            usage: command.usage || command.name || file.replace('.js', ''),
                            cooldown: command.cooldown || 0,
                            aliases: command.aliases || [],
                            examples: command.examples || [],
                            memberpermissions: command.memberpermissions || [],
                            botpermissions: command.botpermissions || [],
                            requiredroles: command.requiredroles || [],
                            requiredchannels: command.requiredchannels || [],
                            alloweduserids: command.alloweduserids || [],
                            minargs: command.minargs || 0,
                            maxargs: command.maxargs || 0,
                            nsfw: command.nsfw || false,
                            OwnerOnly: command.OwnerOnly || false,
                            ServerOwnerOnly: command.ServerOwnerOnly || false,
                            DevloperTeamOnly: command.DevloperTeamOnly || false
                        };

                        commands.set(commandData.name, commandData);
                        categoryCommands.push(commandData);

                        // Check if this command has subcommands (like games)
                        if (command.subcommands && command.subcommands instanceof Map && command.subcommands.size > 0) {
                            command.subcommands.forEach((subcommand, subcommandName) => {
                                const subcommandData = {
                                    name: `${commandData.name} ${subcommandName}`,
                                    description: subcommand.data?.description || subcommand.description || 'No description provided',
                                    category: category,
                                    usage: `${commandData.name} ${subcommandName}`,
                                    cooldown: commandData.cooldown,
                                    aliases: [],
                                    examples: [`${commandData.name} ${subcommandName}`],
                                    memberpermissions: commandData.memberpermissions,
                                    botpermissions: commandData.botpermissions,
                                    requiredroles: commandData.requiredroles,
                                    requiredchannels: commandData.requiredchannels,
                                    alloweduserids: commandData.alloweduserids,
                                    minargs: 0,
                                    maxargs: 0,
                                    nsfw: commandData.nsfw,
                                    OwnerOnly: commandData.OwnerOnly,
                                    ServerOwnerOnly: commandData.ServerOwnerOnly,
                                    DevloperTeamOnly: commandData.DevloperTeamOnly,
                                    isSubcommand: true,
                                    parentCommand: commandData.name
                                };

                                commands.set(subcommandData.name, subcommandData);
                                categoryCommands.push(subcommandData);
                            });
                        }

                    } catch (error) {
                        console.error(`Error loading command ${file}:`, error);
                    }
                });

                // Load commands from subdirectories (like games folder)
                const subdirectories = fs.readdirSync(categoryPath).filter(item => {
                    const itemPath = path.join(categoryPath, item);
                    return fs.lstatSync(itemPath).isDirectory();
                });

                subdirectories.forEach(subdir => {
                    const subdirPath = path.join(categoryPath, subdir);
                    const indexFile = path.join(subdirPath, 'index.js');

                    if (fs.existsSync(indexFile)) {
                        try {
                            const command = require(indexFile);
                            const commandData = {
                                name: command.name || command.data?.name || subdir,
                                description: command.description || 'No description provided',
                                category: category,
                                usage: command.usage || command.name || subdir,
                                cooldown: command.cooldown || 0,
                                aliases: command.aliases || [],
                                examples: command.examples || [],
                                memberpermissions: command.memberpermissions || [],
                                botpermissions: command.botpermissions || [],
                                requiredroles: command.requiredroles || [],
                                requiredchannels: command.requiredchannels || [],
                                alloweduserids: command.alloweduserids || [],
                                minargs: command.minargs || 0,
                                maxargs: command.maxargs || 0,
                                nsfw: command.nsfw || false,
                                OwnerOnly: command.OwnerOnly || false,
                                ServerOwnerOnly: command.ServerOwnerOnly || false,
                                DevloperTeamOnly: command.DevloperTeamOnly || false
                            };

                            commands.set(commandData.name, commandData);
                            categoryCommands.push(commandData);

                            // Check if this command has subcommands (like games)
                            if (command.subcommands && command.subcommands instanceof Map && command.subcommands.size > 0) {
                                command.subcommands.forEach((subcommand, subcommandName) => {
                                    const subcommandData = {
                                        name: `${commandData.name} ${subcommandName}`,
                                        description: subcommand.data?.description || subcommand.description || 'No description provided',
                                        category: category,
                                        usage: `${commandData.name} ${subcommandName}`,
                                        cooldown: commandData.cooldown,
                                        aliases: [],
                                        examples: [`${commandData.name} ${subcommandName}`],
                                        memberpermissions: commandData.memberpermissions,
                                        botpermissions: commandData.botpermissions,
                                        requiredroles: commandData.requiredroles,
                                        requiredchannels: commandData.requiredchannels,
                                        alloweduserids: commandData.alloweduserids,
                                        minargs: 0,
                                        maxargs: 0,
                                        nsfw: commandData.nsfw,
                                        OwnerOnly: commandData.OwnerOnly,
                                        ServerOwnerOnly: commandData.ServerOwnerOnly,
                                        DevloperTeamOnly: commandData.DevloperTeamOnly,
                                        isSubcommand: true,
                                        parentCommand: commandData.name
                                    };

                                    commands.set(subcommandData.name, subcommandData);
                                    categoryCommands.push(subcommandData);
                                });
                            }
                        } catch (error) {
                            console.error(`Error loading command from subdirectory ${subdir}:`, error);
                        }
                    }
                });

                if (categoryCommands.length > 0) {
                    categories.set(category, categoryCommands);
                }
            }
        });

        return { commands, categories };
    }

    async handleCommandHelp(interaction, commandName, commands, prefix, client, canvasManager, timer, showSpeed) {
        const command = commands.get(commandName) || [...commands.values()].find(cmd => cmd.aliases?.includes(commandName));
        timer.mark('command-lookup');
        
        if (!command) {
            return interaction.reply({ 
                content: `❌ Command \`${commandName}\` not found. Use \`/help\` to see all commands.`,
                ephemeral: true 
            });
        }
        
        const [commandPageBuffer, backButton] = await Promise.all([
            canvasManager.generateCommandPage(command, prefix, client),
            Promise.resolve(this.getBackButton())
        ]);
        timer.mark('command-page-gen');
        
        const commandAttachment = new AttachmentBuilder(commandPageBuffer, { name: `cmd-${command.name}.png` });
        timer.mark('cmd-attachment-create');
        
        const helpMessage = await interaction.editReply({ 
            content: showSpeed ? timer.getReport() : undefined,
            files: [commandAttachment], 
            components: [new ActionRowBuilder().addComponents(backButton)] 
        });
        timer.mark('cmd-message-send');
        
        this.setupCollectors(helpMessage, interaction.user.id, client, prefix, this.getCommandsAndCategories(client).categories, commands, canvasManager, showSpeed);
        timer.mark('cmd-collectors-setup');
        
        if (showSpeed) {
            setTimeout(() => {
                helpMessage.edit({
                    content: timer.getReport(),
                    files: [commandAttachment],
                    components: [new ActionRowBuilder().addComponents(backButton)]
                }).catch(() => {});
            }, 100);
        }
    }

    stopCollectors(messageId) {
        const collectors = this.collectors.get(messageId);
        if (collectors) {
            collectors.forEach(c => c.stop('override'));
            this.collectors.delete(messageId);
        }
    }

    setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed) {
        this.stopCollectors(helpMessage.id);
        
        const selectCollector = helpMessage.createMessageComponentCollector({
            componentType: ComponentType.StringSelect,
            time: TIMEOUT
        });

        const buttonCollector = helpMessage.createMessageComponentCollector({
            componentType: ComponentType.Button,
            time: TIMEOUT
        });
        
        this.collectors.set(helpMessage.id, [selectCollector, buttonCollector]);
        
        selectCollector.on('collect', async (interaction) => {
            if (interaction.user.id !== authorId) {
                return interaction.reply({ content: 'This menu is only for the command user.', ephemeral: true });
            }
            
            const timer = Timer.create();
            timer.mark('interaction-start');
            
            await interaction.deferUpdate();
            timer.mark('defer-update');
            
            this.resetTimers(selectCollector, buttonCollector);
            timer.mark('reset-timers');
            
            const selectedCategory = interaction.values[0];
            timer.mark('get-selection');
            
            if (selectedCategory === 'home') {
                const [homePageBuffer, components] = await Promise.all([
                    canvasManager.generateHomePage(client, prefix, categories),
                    Promise.resolve([this.getMainSelectMenu(categories)])
                ]);
                timer.mark('home-regen');
                
                const homeAttachment = new AttachmentBuilder(homePageBuffer, { name: 'help-home.png' });
                timer.mark('home-attachment');
                
                await interaction.editReply({ 
                    content: showSpeed ? timer.getReport() : undefined,
                    files: [homeAttachment], 
                    components 
                });
                timer.mark('home-edit-reply');
                
                this.setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed);
                timer.mark('home-setup-collectors');
                return;
            }
            
            const categoryCommands = categories.get(selectedCategory) || [];
            timer.mark('get-category-commands');
            
            const [categoryPageBuffer, components] = await Promise.all([
                canvasManager.generateCategoryPage(selectedCategory, categoryCommands, prefix, client, 1, ITEMS_PER_PAGE),
                Promise.resolve(this.getCategoryComponents(selectedCategory, categoryCommands, categories))
            ]);
            timer.mark('category-page-gen');
            
            const categoryAttachment = new AttachmentBuilder(categoryPageBuffer, { name: `cat-${selectedCategory}-p1.png` });
            timer.mark('category-attachment');
            
            await interaction.editReply({ 
                content: showSpeed ? timer.getReport() : undefined,
                files: [categoryAttachment], 
                components 
            });
            timer.mark('category-edit-reply');
            
            this.setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed);
            timer.mark('category-setup-collectors');
        });
        
        buttonCollector.on('collect', async (interaction) => {
            if (interaction.user.id !== authorId) {
                return interaction.reply({ content: 'This button is only for the command user.', ephemeral: true });
            }
            
            const timer = Timer.create();
            timer.mark('button-start');
            
            await interaction.deferUpdate();
            timer.mark('button-defer');
            
            this.resetTimers(selectCollector, buttonCollector);
            timer.mark('button-reset-timers');
            
            const [action, ...params] = interaction.customId.split('_');
            timer.mark('parse-action');
            
            console.log(`🔘 Button clicked: ${interaction.customId}`);
            
            switch (interaction.customId) {
                case 'help_cmd_back_home':
                case 'back_to_home_btn':
                    console.log('🏠 Handling home navigation');
                    await this.handleHomeNavigation(interaction, canvasManager, client, prefix, categories, helpMessage, authorId, commands, timer, showSpeed);
                    break;
                    
                case 'search_command_btn':
                    console.log('🔍 Handling search command');
                    await this.handleSearchCommand(interaction, helpMessage, authorId, client, prefix, categories, commands, canvasManager, timer, showSpeed);
                    break;
                    
                default:
                    console.log(`🔧 Handling custom action: ${action} with params:`, params);
                    if (action === 'catpage') {
                        await this.handleCategoryPagination(interaction, params, categories, canvasManager, prefix, client, helpMessage, authorId, commands, timer, showSpeed);
                    } else if (action === 'searchpage') {
                        await this.handleSearchPagination(interaction, params, commands, canvasManager, prefix, client, helpMessage, authorId, categories, timer, showSpeed);
                    } else {
                        console.log(`❓ Unknown button action: ${interaction.customId}`);
                        await interaction.followUp({ 
                            content: `Unknown button action: ${interaction.customId}`, 
                            ephemeral: true 
                        });
                    }
            }
        });
        
        this.setupCollectorCleanup(selectCollector, buttonCollector, helpMessage);
    }

    async handleHomeNavigation(interaction, canvasManager, client, prefix, categories, helpMessage, authorId, commands, timer, showSpeed) {
        console.log('🏠 Generating home page navigation...');
        
        try {
            const [homePageBuffer, components] = await Promise.all([
                canvasManager.generateHomePage(client, prefix, categories),
                Promise.resolve([this.getMainSelectMenu(categories)])
            ]);
            timer.mark('home-nav-gen');
            console.log('✅ Home page generated successfully');
            
            const homeAttachment = new AttachmentBuilder(homePageBuffer, { name: 'help-home.png' });
            timer.mark('home-nav-attachment');
            
            await interaction.editReply({ 
                content: showSpeed ? timer.getReport() : undefined,
                files: [homeAttachment], 
                components 
            });
            timer.mark('home-nav-edit');
            console.log('✅ Home page displayed successfully');
            
            // Clear old collectors and set up new ones
            this.stopCollectors(helpMessage.id);
            this.setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed);
            timer.mark('home-nav-collectors');
            console.log('✅ Home collectors setup complete');
            
        } catch (error) {
            console.error('❌ Error in home navigation:', error);
            await interaction.followUp({ 
                content: `❌ Error returning to home: ${error.message}`, 
                ephemeral: true 
            });
        }
    }

    async handleCategoryPagination(interaction, params, categories, canvasManager, prefix, client, helpMessage, authorId, commands, timer, showSpeed) {
        console.log('📄 Handling category pagination with params:', params);
        
        const [categoryName, pageStr] = params;
        const page = Math.max(1, parseInt(pageStr, 10) || 1);
        timer.mark('parse-cat-params');
        
        console.log(`📖 Loading page ${page} for category: ${categoryName}`);
        
        const categoryCommands = categories.get(categoryName) || [];
        timer.mark('get-cat-commands');
        
        if (categoryCommands.length === 0) {
            await interaction.followUp({ 
                content: `❌ No commands found in category: ${categoryName}`, 
                ephemeral: true 
            });
            return;
        }
        
        const totalPages = Math.ceil(categoryCommands.length / ITEMS_PER_PAGE);
        const validPage = Math.min(Math.max(1, page), totalPages);
        
        console.log(`📊 Category ${categoryName}: ${categoryCommands.length} commands, page ${validPage}/${totalPages}`);
        
        try {
            const [categoryPageBuffer, components] = await Promise.all([
                canvasManager.generateCategoryPage(categoryName, categoryCommands, prefix, client, validPage, ITEMS_PER_PAGE),
                Promise.resolve(this.getCategoryComponents(categoryName, categoryCommands, categories, validPage))
            ]);
            timer.mark('cat-page-gen');
            
            const categoryAttachment = new AttachmentBuilder(categoryPageBuffer, { name: `cat-${categoryName}-p${validPage}.png` });
            timer.mark('cat-page-attachment');
            
            await interaction.editReply({ 
                content: showSpeed ? timer.getReport() : undefined,
                files: [categoryAttachment], 
                components 
            });
            timer.mark('cat-page-edit');
            console.log('✅ Category page updated successfully');
            
            // Clear old collectors and set up new ones
            this.stopCollectors(helpMessage.id);
            this.setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed);
            timer.mark('cat-page-collectors');
            
        } catch (error) {
            console.error('❌ Error in category pagination:', error);
            await interaction.followUp({ 
                content: `❌ Error loading page: ${error.message}`, 
                ephemeral: true 
            });
        }
    }

    async handleSearchPagination(interaction, params, commands, canvasManager, prefix, client, helpMessage, authorId, categories, timer, showSpeed) {
        console.log('🔍 Handling search pagination with params:', params);
        
        const [encodedSearchTerm, pageStr] = params;
        const searchTerm = decodeURIComponent(encodedSearchTerm);
        const page = Math.max(1, parseInt(pageStr, 10) || 1);
        timer.mark('parse-search-params');
        
        console.log(`🔍 Search pagination: "${searchTerm}" page ${page}`);
        
        const filteredCommands = this.getFilteredCommands(commands, searchTerm);
        timer.mark('filter-search-commands');
        
        if (filteredCommands.length === 0) {
            await interaction.followUp({ 
                content: `❌ No commands found for: ${searchTerm}`, 
                ephemeral: true 
            });
            return;
        }
        
        const totalPages = Math.ceil(filteredCommands.length / ITEMS_PER_PAGE);
        const validPage = Math.min(Math.max(1, page), totalPages);
        
        console.log(`📊 Search results: ${filteredCommands.length} commands, page ${validPage}/${totalPages}`);
        
        try {
            const [searchPageBuffer, components] = await Promise.all([
                canvasManager.generateSearchResultsPage(searchTerm, filteredCommands, prefix, client, validPage, ITEMS_PER_PAGE),
                Promise.resolve([this.getSearchPaginationRow(searchTerm, validPage, totalPages)])
            ]);
            timer.mark('search-page-gen');
            
            const searchAttachment = new AttachmentBuilder(searchPageBuffer, { name: `search-${searchTerm}-p${validPage}.png` });
            timer.mark('search-attachment');
            
            await interaction.editReply({ 
                content: showSpeed ? timer.getReport() : undefined,
                files: [searchAttachment], 
                components 
            });
            timer.mark('search-edit');
            console.log('✅ Search page updated successfully');
            
            // Clear old collectors and set up new ones
            this.stopCollectors(helpMessage.id);
            this.setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed);
            timer.mark('search-collectors');
            
        } catch (error) {
            console.error('❌ Error in search pagination:', error);
            await interaction.followUp({ 
                content: `❌ Error loading search page: ${error.message}`, 
                ephemeral: true 
            });
        }
    }

    async handleSearchCommand(interaction, helpMessage, authorId, client, prefix, categories, commands, canvasManager, timer, showSpeed) {
        console.log('🔍 Search command initiated');
        
        // Create a simple search with common terms
        const searchOptions = [
            { label: 'Admin Commands', value: 'admin', description: 'Search for admin commands' },
            { label: 'Fun Commands', value: 'fun', description: 'Search for fun commands' },
            { label: 'Info Commands', value: 'info', description: 'Search for info commands' },
            { label: 'Moderation Commands', value: 'moderation', description: 'Search for moderation commands' },
            { label: 'All Commands', value: 'all', description: 'Show all commands' }
        ];
        
        const searchSelectMenu = new StringSelectMenuBuilder()
            .setCustomId('search_select_menu')
            .setPlaceholder('🔍 Select what to search for...')
            .addOptions(searchOptions);
        
        const searchRow = new ActionRowBuilder().addComponents(searchSelectMenu);
        const backRow = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId('back_to_home_btn')
                .setLabel('Back to Home')
                .setEmoji('🏠')
                .setStyle(ButtonStyle.Secondary)
        );
        
        await interaction.editReply({
            content: '🔍 **Search Commands**\nSelect a category to search or use the dropdown below:',
            files: [],
            components: [searchRow, backRow]
        });
        
        // Set up search collector
        const searchCollector = helpMessage.createMessageComponentCollector({
            filter: (i) => i.customId === 'search_select_menu' && i.user.id === authorId,
            time: 60000
        });

        searchCollector.on('collect', async (searchInteraction) => {
            try {
                await searchInteraction.deferUpdate();
                const searchValue = searchInteraction.values[0];

                console.log(`🎯 Search selected: ${searchValue}`);

                let filteredCommands = [];
                let searchTerm = searchValue;

                if (searchValue === 'all') {
                    filteredCommands = [...commands.values()];
                    searchTerm = 'all commands';
                } else {
                    filteredCommands = [...commands.values()].filter(cmd =>
                        cmd.category.toLowerCase() === searchValue.toLowerCase() ||
                        cmd.name.toLowerCase().includes(searchValue.toLowerCase()) ||
                        cmd.description.toLowerCase().includes(searchValue.toLowerCase())
                    );
                }

                if (filteredCommands.length === 0) {
                    await searchInteraction.editReply({
                        content: `❌ No commands found for "${searchValue}"`,
                        components: [backRow]
                    });
                    return;
                }

                const [searchPageBuffer, components] = await Promise.all([
                    canvasManager.generateSearchResultsPage(searchTerm, filteredCommands, prefix, client, 1, ITEMS_PER_PAGE),
                    Promise.resolve([this.getSearchPaginationRow(searchTerm, 1, Math.ceil(filteredCommands.length / ITEMS_PER_PAGE))])
                ]);

                const searchAttachment = new AttachmentBuilder(searchPageBuffer, { name: `search-${searchValue}-p1.png` });

                await searchInteraction.editReply({
                    content: showSpeed ? timer.getReport() : undefined,
                    files: [searchAttachment],
                    components
                });

                // Stop the search collector and setup new collectors
                searchCollector.stop();
                this.setupCollectors(helpMessage, authorId, client, prefix, categories, commands, canvasManager, showSpeed);
            } catch (error) {
                console.error('❌ Error in search collector:', error);
                await searchInteraction.followUp({
                    content: '❌ An error occurred while searching. Please try again.',
                    ephemeral: true
                }).catch(() => {});
            }
        });

        searchCollector.on('end', () => {
            console.log('🔍 Search collector ended');
        });
    }

    setupCollectorCleanup(selectCollector, buttonCollector, helpMessage) {
        const handleEnd = (reason) => {
            if (reason === 'override') return;
            
            if (this.collectors.has(helpMessage.id)) {
                this.collectors.get(helpMessage.id).forEach(c => {
                    if (!c.ended) c.stop('final_cleanup');
                });
                this.collectors.delete(helpMessage.id);
            }
            
            if (reason === 'time' && !helpMessage.deleted) {
                helpMessage.edit({ 
                    content: 'This help menu has timed out. Run the command again if needed.', 
                    components: [] 
                }).catch(err => console.warn('HelpCommand', 'Failed to edit message on timeout:', err.message));
            }
        };
        
        selectCollector.on('end', (_, reason) => handleEnd(reason));
        buttonCollector.on('end', (_, reason) => handleEnd(reason));
    }

    resetTimers(...collectors) {
        collectors.forEach(collector => collector.resetTimer());
    }

    getFilteredCommands(commands, searchTerm) {
        const cacheKey = `${searchTerm}-${commands.size}`;
        if (this.cache.filteredCommands.has(cacheKey)) {
            return this.cache.filteredCommands.get(cacheKey);
        }
        
        const term = searchTerm.toLowerCase();
        const filtered = [...commands.values()].filter(cmd => 
            cmd.name.toLowerCase().includes(term) || 
            cmd.aliases?.some(alias => alias.toLowerCase().includes(term))
        );
        
        this.cache.filteredCommands.set(cacheKey, filtered);
        return filtered;
    }

    getCategoryOptions(categories) {
        const cacheKey = [...categories.keys()].sort().join(',');
        if (this.cache.categoryOptions.has(cacheKey)) {
            return this.cache.categoryOptions.get(cacheKey);
        }
        
        const options = [
            { label: 'Home', description: 'Return to the main help menu', value: 'home' },
            ...[...categories.keys()]
                .filter(cat => !['developer', 'owner'].includes(cat.toLowerCase()))
                .sort()
                .map(cat => ({
                    label: this.formatCategoryName(cat),
                    description: `View ${this.formatCategoryName(cat)} commands`,
                    value: cat
                }))
        ];
        
        this.cache.categoryOptions.set(cacheKey, options);
        return options;
    }

    getMainSelectMenu(categories) {
        const cacheKey = `main-${[...categories.keys()].sort().join(',')}`;
        if (this.cache.components.has(cacheKey)) {
            return this.cache.components.get(cacheKey);
        }
        
        const component = new ActionRowBuilder().addComponents(
            new StringSelectMenuBuilder()
                .setCustomId('help_category_select_menu_main')
                .setPlaceholder('Select a category...')
                .setOptions(this.getCategoryOptions(categories))
        );
        
        this.cache.components.set(cacheKey, component);
        return component;
    }

    getCategoryComponents(categoryName, categoryCommands, categories, page = 1) {
        const totalPages = Math.ceil(categoryCommands.length / ITEMS_PER_PAGE);
        const cacheKey = `cat-${categoryName}-${page}-${totalPages}`;
        
        if (this.cache.components.has(cacheKey)) {
            return this.cache.components.get(cacheKey);
        }
        
        const components = [this.getMainSelectMenu(categories)];
        if (categoryCommands.length > 0) {
            components.push(this.getCategoryPaginationRow(categoryName, page, totalPages));
        }
        
        this.cache.components.set(cacheKey, components);
        return components;
    }

    getCategoryPaginationRow(categoryName, currentPage, totalPages) {
        const cacheKey = `catpag-${categoryName}-${currentPage}-${totalPages}`;
        if (this.cache.components.has(cacheKey)) {
            return this.cache.components.get(cacheKey);
        }

        const component = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId(`catpage_${categoryName}_${currentPage - 1}`)
                .setEmoji('◀️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === 1),
            new ButtonBuilder()
                .setCustomId('search_command_btn')
                .setLabel('Search')
                .setEmoji('🔍')
                .setStyle(ButtonStyle.Success),
            new ButtonBuilder()
                .setCustomId(`catpage_${categoryName}_${currentPage + 1}`)
                .setEmoji('▶️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === totalPages || totalPages <= 1)
        );

        this.cache.components.set(cacheKey, component);
        return component;
    }

    getSearchPaginationRow(searchTerm, currentPage, totalPages) {
        const cacheKey = `searchpag-${searchTerm}-${currentPage}-${totalPages}`;
        if (this.cache.components.has(cacheKey)) {
            return this.cache.components.get(cacheKey);
        }
        
        const component = new ActionRowBuilder().addComponents(
            new ButtonBuilder()
                .setCustomId(`searchpage_${encodeURIComponent(searchTerm)}_${currentPage - 1}`)
                .setEmoji('◀️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === 1),
            new ButtonBuilder()
                .setCustomId('back_to_home_btn')
                .setLabel('Home')
                .setEmoji('🏠')
                .setStyle(ButtonStyle.Secondary),
            new ButtonBuilder()
                .setCustomId(`searchpage_${encodeURIComponent(searchTerm)}_${currentPage + 1}`)
                .setEmoji('▶️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === totalPages || totalPages <= 1)
        );
        
        this.cache.components.set(cacheKey, component);
        return component;
    }

    getBackButton() {
        const cacheKey = 'back-button';
        if (this.cache.components.has(cacheKey)) {
            return this.cache.components.get(cacheKey);
        }
        
        const button = new ButtonBuilder()
            .setCustomId('help_cmd_back_home')
            .setLabel('Back to Home')
            .setStyle(ButtonStyle.Secondary);
        
        this.cache.components.set(cacheKey, button);
        return button;
    }

    formatCategoryName(category) {
        return category ? category.charAt(0).toUpperCase() + category.slice(1).toLowerCase() : 'Unknown';
    }
}

module.exports = new HelpCommand();