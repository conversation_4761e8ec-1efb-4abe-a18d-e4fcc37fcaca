const { performance } = require('perf_hooks');

/**
 * Performance timing utility for debugging and optimization
 */
class Timer {
    constructor() {
        this.marks = new Map();
        this.start = performance.now();
    }

    static create() {
        return new Timer();
    }

    mark(step) {
        this.marks.set(step, performance.now() - this.start);
    }

    getReport() {
        const steps = [...this.marks.entries()];
        let report = `⚡ **Speed Report** (${steps.length} steps)\n\`\`\`diff\n`;
        
        let lastTime = 0;
        for (const [step, time] of steps) {
            const stepTime = time - lastTime;
            const prefix = stepTime > 100 ? '- ' : stepTime > 50 ? '  ' : '+ ';
            report += `${prefix}${step.padEnd(20)}: ${stepTime.toFixed(1)}ms (${time.toFixed(1)}ms)\n`;
            lastTime = time;
        }
        
        const total = steps[steps.length - 1]?.[1] || 0;
        report += `${'-'.repeat(35)}\n`;
        report += `+ TOTAL: ${total.toFixed(1)}ms\n\`\`\``;
        
        return report;
    }
}

module.exports = Timer;
